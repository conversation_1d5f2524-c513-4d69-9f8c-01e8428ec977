import Anthropic from '@anthropic-ai/sdk';

// 测试 Anthropic 模型列表功能
async function testAnthropicModels() {
  try {
    // 使用您的 API 密钥
    const apiKey = '************************************************************************************************************';

    console.log('正在通过代理获取 Anthropic 模型列表...');

    // 通过本地代理调用 Anthropic API
    const response = await fetch('http://localhost:3001/api/anthropic/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      }
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API 请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('获取到的模型数据:', JSON.stringify(data, null, 2));

    // 解析模型列表
    const models = data.data || [];
    console.log(`\n找到 ${models.length} 个模型:`);

    models.forEach((model, index) => {
      console.log(`${index + 1}. ${model.id}`);
      if (model.display_name) {
        console.log(`   显示名称: ${model.display_name}`);
      }
      if (model.created_at) {
        console.log(`   创建时间: ${model.created_at}`);
      }
      console.log('');
    });

  } catch (error) {
    console.error('测试失败:', error);
  }
}

// 测试前端 LLMService
async function testLLMService() {
  try {
    console.log('正在测试 LLMService...');

    // 动态导入 LLMService
    const { LLMService } = await import('../src/services/OpenAIService.ts');

    const llmService = new LLMService({
      apiKey: '************************************************************************************************************',
      providerId: 'anthropic'
    });

    const models = await llmService.getModels('anthropic');
    console.log('LLMService 获取到的模型:', models);

  } catch (error) {
    console.error('LLMService 测试失败:', error);
  }
}

// 测试消息发送功能
async function testAnthropicMessage() {
  try {
    const anthropic = new Anthropic({
      apiKey: '************************************************************************************************************',
    });

    console.log('正在发送测试消息...');

    const msg = await anthropic.messages.create({
      model: "claude-3-haiku-20240307",
      max_tokens: 1024,
      messages: [{ role: "user", content: "Hello, Claude, write a joke" }],
    });

    console.log('消息响应:', msg);
  } catch (error) {
    console.error('消息测试失败:', error);
  }
}

// 运行测试
if (import.meta.url === `file://${process.argv[1]}`) {
  console.log('=== Anthropic API 测试 ===\n');

  // 首先测试模型列表
  await testAnthropicModels();

  console.log('\n=== 测试 LLMService ===\n');

  // 测试 LLMService
  await testLLMService();

  console.log('\n=== 测试消息发送 ===\n');

  // 然后测试消息发送
  // await testAnthropicMessage();
}

export { testAnthropicModels, testAnthropicMessage, testLLMService };