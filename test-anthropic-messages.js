// 测试 Anthropic 消息发送功能
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试非流式消息发送
async function testAnthropicMessage() {
  try {
    console.log('=== 测试 Anthropic 非流式消息发送 ===\n');
    
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: [
        {
          role: 'user',
          content: 'Hello! Please write a short joke about programming.'
        }
      ],
      temperature: 0.7
    };
    
    console.log('发送请求:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch('http://localhost:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }
    
    const data = await response.json();
    console.log('响应数据:', JSON.stringify(data, null, 2));
    
    if (data.content && data.content.length > 0) {
      const content = data.content[0];
      if (content.type === 'text') {
        console.log('\n✅ 消息发送成功！');
        console.log('Claude 的回复:', content.text);
      }
    }
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试流式消息发送
async function testAnthropicStreamMessage() {
  try {
    console.log('\n=== 测试 Anthropic 流式消息发送 ===\n');
    
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: [
        {
          role: 'user',
          content: 'Please tell me a story about a robot learning to paint. Make it about 3 paragraphs long.'
        }
      ],
      temperature: 0.7,
      stream: true
    };
    
    console.log('发送流式请求:', JSON.stringify(requestBody, null, 2));
    
    const response = await fetch('http://localhost:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`流式请求失败: ${response.status}`, errorText);
      return;
    }
    
    console.log('开始接收流式响应...\n');
    
    const reader = response.body?.getReader();
    if (!reader) {
      console.error('无法获取响应流');
      return;
    }
    
    const decoder = new TextDecoder();
    let fullResponse = '';
    
    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;
        
        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');
        
        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }
            
            try {
              const parsed = JSON.parse(data);
              
              // 处理文本内容
              if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                const text = parsed.delta.text;
                process.stdout.write(text);
                fullResponse += text;
              }
              
              // 处理消息开始
              if (parsed.type === 'message_start') {
                console.log('消息开始...');
              }
              
              // 处理消息结束
              if (parsed.type === 'message_stop') {
                console.log('\n\n✅ 流式消息接收完成！');
              }
              
            } catch (parseError) {
              // 忽略解析错误，可能是不完整的数据
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }
    
    console.log('\n完整响应长度:', fullResponse.length, '字符');
    
  } catch (error) {
    console.error('❌ 流式测试失败:', error.message);
  }
}

// 测试带 system prompt 的消息
async function testAnthropicWithSystem() {
  try {
    console.log('\n=== 测试带 System Prompt 的消息 ===\n');
    
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 500,
      system: 'You are a helpful assistant that always responds in a cheerful and encouraging tone.',
      messages: [
        {
          role: 'user',
          content: 'I am feeling a bit down today. Can you cheer me up?'
        }
      ],
      temperature: 0.7
    };
    
    console.log('发送带 system prompt 的请求...');
    
    const response = await fetch('http://localhost:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }
    
    const data = await response.json();
    
    if (data.content && data.content.length > 0) {
      const content = data.content[0];
      if (content.type === 'text') {
        console.log('✅ System prompt 测试成功！');
        console.log('Claude 的鼓励回复:', content.text);
      }
    }
    
  } catch (error) {
    console.error('❌ System prompt 测试失败:', error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始测试 Anthropic 消息发送功能...\n');
  
  // 测试非流式消息
  await testAnthropicMessage();
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试流式消息
  await testAnthropicStreamMessage();
  
  // 等待一下
  await new Promise(resolve => setTimeout(resolve, 2000));
  
  // 测试 system prompt
  await testAnthropicWithSystem();
  
  console.log('\n🎉 所有测试完成！');
}

// 运行测试
runAllTests().catch(console.error);
