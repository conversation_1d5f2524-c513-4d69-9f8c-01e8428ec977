// 简单的模型列表测试脚本
import fetch from 'node-fetch';

async function testModelsAPI() {
  try {
    console.log('正在测试 Anthropic 模型列表 API...');
    
    const apiKey = '************************************************************************************************************';
    
    const response = await fetch('http://localhost:3001/api/anthropic/models', {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'anthropic-version': '2023-06-01',
        'Content-Type': 'application/json'
      }
    });
    
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`API 请求失败: ${response.status}`, errorText);
      return;
    }
    
    const data = await response.json();
    console.log('获取到的模型数据:', JSON.stringify(data, null, 2));
    
    // 解析模型列表
    const models = data.data || [];
    console.log(`\n找到 ${models.length} 个模型:`);
    
    models.forEach((model, index) => {
      console.log(`${index + 1}. ${model.id}`);
      if (model.display_name) {
        console.log(`   显示名称: ${model.display_name}`);
      }
      if (model.created_at) {
        console.log(`   创建时间: ${model.created_at}`);
      }
      console.log('');
    });
    
    console.log('✅ 模型列表 API 测试成功！');
    
  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testModelsAPI();
