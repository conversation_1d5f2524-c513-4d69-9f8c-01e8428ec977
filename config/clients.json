{"filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/dev/pyproject/huggingface/demo1/qwen3", "/Users/<USER>/Desktop", "/Users/<USER>/Downloads"], "description": "filesystem", "lastUpdated": "2025-06-10T08:29:11.808Z"}, "time": {"command": "uvx", "args": ["mcp-server-time", "--local-timezone=Asia/Shanghai"], "description": "time", "lastUpdated": "2025-06-10T08:29:12.218Z"}, "weather": {"command": "uv", "args": ["--directory", "/Users/<USER>/dev/mcpproject/rim-mcp", "run", "rim-mcp.py"], "description": "天气服务", "lastUpdated": "2025-06-10T08:29:12.373Z"}}