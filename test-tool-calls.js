// 测试 Anthropic 工具调用参数传递
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试非流式工具调用
async function testNonStreamToolCall() {
  console.log('🔧 测试非流式工具调用...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: 'Please write a file called "test.txt" with the content "Hello, World!"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送非流式工具调用请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 非流式工具调用响应:');
    console.log(JSON.stringify(data, null, 2));

    // 检查是否有工具调用
    if (data.content) {
      for (const content of data.content) {
        if (content.type === 'tool_use') {
          console.log('\n🎯 发现工具调用:');
          console.log('工具名称:', content.name);
          console.log('工具参数:', JSON.stringify(content.input, null, 2));
        }
      }
    }

  } catch (error) {
    console.error('❌ 非流式工具调用测试失败:', error.message);
  }
}

// 测试流式工具调用
async function testStreamToolCall() {
  console.log('\n🌊 测试流式工具调用...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: 'Please create a file named "hello.py" with a simple Python script that prints "Hello, Python!"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    stream: true,
    temperature: 0.7
  };

  try {
    console.log('发送流式工具调用请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`流式请求失败: ${response.status}`, errorText);
      return;
    }

    console.log('开始接收流式响应...\n');

    const reader = response.body?.getReader();
    if (!reader) {
      console.error('无法获取响应流');
      return;
    }

    const decoder = new TextDecoder();
    let fullResponse = '';

    try {
      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        const chunk = decoder.decode(value, { stream: true });
        const lines = chunk.split('\n');

        for (const line of lines) {
          if (line.startsWith('data: ')) {
            const data = line.slice(6);
            if (data === '[DONE]') {
              break;
            }

            try {
              const parsed = JSON.parse(data);
              
              // 显示所有事件类型
              console.log(`📡 事件类型: ${parsed.type}`);
              
              if (parsed.type === 'content_block_start' && parsed.content_block?.type === 'tool_use') {
                console.log('🔧 工具调用开始:');
                console.log('  工具名称:', parsed.content_block.name);
                console.log('  工具ID:', parsed.content_block.id);
              }
              
              if (parsed.type === 'content_block_delta' && parsed.delta?.type === 'input_json_delta') {
                console.log('📝 工具参数增量:', parsed.delta.partial_json);
              }
              
              if (parsed.type === 'content_block_stop') {
                console.log('✅ 内容块结束');
              }
              
              // 处理文本内容
              if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                process.stdout.write(parsed.delta.text);
                fullResponse += parsed.delta.text;
              }

            } catch (parseError) {
              // 忽略解析错误
            }
          }
        }
      }
    } finally {
      reader.releaseLock();
    }

    console.log('\n\n✅ 流式工具调用测试完成');
    console.log('完整响应长度:', fullResponse.length, '字符');

  } catch (error) {
    console.error('❌ 流式工具调用测试失败:', error.message);
  }
}

// 测试复杂工具调用
async function testComplexToolCall() {
  console.log('\n🎯 测试复杂工具调用...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: 'Please search for information about "artificial intelligence" and then write the results to a file called "ai_info.txt"'
      }
    ],
    tools: [
      {
        name: 'web_search',
        description: 'Search the web for information',
        input_schema: {
          type: 'object',
          properties: {
            query: {
              type: 'string',
              description: 'The search query'
            },
            num_results: {
              type: 'integer',
              description: 'Number of results to return',
              default: 5
            }
          },
          required: ['query']
        }
      },
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送复杂工具调用请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 复杂工具调用响应:');
    console.log(JSON.stringify(data, null, 2));

    // 分析工具调用
    if (data.content) {
      for (const content of data.content) {
        if (content.type === 'tool_use') {
          console.log('\n🎯 发现工具调用:');
          console.log('工具名称:', content.name);
          console.log('工具参数:');
          console.log(JSON.stringify(content.input, null, 2));
          
          // 验证参数完整性
          if (content.name === 'web_search') {
            if (content.input && content.input.query) {
              console.log('✅ web_search 参数完整');
            } else {
              console.log('❌ web_search 参数缺失');
            }
          }
          
          if (content.name === 'write_file') {
            if (content.input && content.input.path && content.input.content) {
              console.log('✅ write_file 参数完整');
            } else {
              console.log('❌ write_file 参数缺失');
            }
          }
        }
      }
    }

  } catch (error) {
    console.error('❌ 复杂工具调用测试失败:', error.message);
  }
}

// 运行所有测试
async function runAllToolCallTests() {
  console.log('🚀 开始工具调用参数传递测试...\n');
  
  await testNonStreamToolCall();
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  await testStreamToolCall();
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  await testComplexToolCall();
  
  console.log('\n🎉 所有工具调用测试完成！');
}

// 运行测试
runAllToolCallTests().catch(console.error);
