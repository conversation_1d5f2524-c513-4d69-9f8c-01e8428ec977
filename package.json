{"name": "mcp-chat", "private": true, "version": "1.0.0", "description": "一个强大的AI驱动的聊天应用，支持多种AI模型和MCP工具集成", "author": {"name": "<EMAIL>", "url": "https://github.com/machaojin1917939763"}, "type": "module", "main": "electron/main.cjs", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "server": "node electron/mcp_server.cjs", "mcp:server": "cross-env NODE_ENV=development node electron/mcp_server.cjs", "electron:dev": "concurrently \"npm run dev\" \"npm run electron:start\"", "electron:start": "cross-env NODE_ENV=development electron .", "electron:build": "npm run build && electron-builder", "electron:buildwin": "npm run build && electron-builder --win", "electron:buildmac": "npm run build && electron-builder --mac", "electron:buildlinux": "npm run build && electron-builder --linux"}, "dependencies": {"@ant-design/icons": "^6.0.0", "@anthropic-ai/sdk": "^0.39.0", "@modelcontextprotocol/sdk": "^1.8.0", "@types/katex": "^0.16.7", "antd": "^5.24.6", "anthropic": "^0.0.0", "axios": "^1.8.4", "body-parser": "^1.20.2", "cors": "^2.8.5", "dayjs": "^1.11.13", "dompurify": "^3.2.5", "dotenv": "^16.4.7", "dotenv-expand": "^10.0.0", "express": "^4.18.2", "fs-extra": "^11.3.0", "highlight.js": "^11.11.1", "katex": "^0.16.21", "markdown-it": "^14.1.0", "markdown-it-katex": "^2.0.3", "marked": "^15.0.7", "marked-katex-extension": "^5.1.4", "mcp": "^1.4.2", "node-fetch": "^3.3.2", "openai": "^4.91.1", "rehype-katex": "^7.0.1", "remark-math": "^6.0.0", "vue": "^3.5.13"}, "devDependencies": {"@types/dompurify": "^3.0.5", "@types/markdown-it": "^14.1.2", "@types/marked": "^5.0.2", "@types/node": "^22.14.0", "@vitejs/plugin-vue": "^5.2.1", "@vue/tsconfig": "^0.7.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^30.5.1", "electron-builder": "^24.13.3", "typescript": "~5.7.2", "vite": "^6.2.0", "vue-tsc": "^2.2.4"}}