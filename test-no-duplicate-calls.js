// 测试重复工具调用修复效果
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试简单文件写入（最容易触发重复调用的场景）
async function testSimpleFileWrite() {
  console.log('🧪 测试简单文件写入（检查是否有重复调用）...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '请创建一个名为 test_no_duplicate.txt 的文件，内容是 "Hello, this is a test file!"'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 响应成功接收');
    
    // 分析响应中的工具调用
    let toolCallCount = 0;
    if (data.content) {
      for (const content of data.content) {
        if (content.type === 'tool_use') {
          toolCallCount++;
          console.log(`🔧 发现工具调用 #${toolCallCount}:`);
          console.log(`   工具名称: ${content.name}`);
          console.log(`   工具参数:`, JSON.stringify(content.input, null, 2));
        }
      }
    }

    console.log(`\n📊 总计工具调用次数: ${toolCallCount}`);
    
    if (toolCallCount === 1) {
      console.log('✅ 测试通过：只有一次工具调用，没有重复');
    } else if (toolCallCount > 1) {
      console.log('❌ 测试失败：检测到重复的工具调用');
    } else {
      console.log('⚠️  未检测到工具调用，可能是纯文本响应');
    }

    // 显示完整响应以供分析
    console.log('\n📄 完整响应内容:');
    console.log(JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 测试复杂场景（多步骤操作）
async function testComplexScenario() {
  console.log('\n🎯 测试复杂场景（多步骤操作）...\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1500,
    messages: [
      {
        role: 'user',
        content: '请帮我创建一个Python脚本文件 hello_world.py，内容是打印 "Hello, World!" 的代码，然后告诉我文件创建的详细信息'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送复杂场景请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 复杂场景响应成功接收');
    
    // 分析响应中的工具调用
    let toolCallCount = 0;
    const toolCalls = [];
    
    if (data.content) {
      for (const content of data.content) {
        if (content.type === 'tool_use') {
          toolCallCount++;
          toolCalls.push({
            name: content.name,
            input: content.input
          });
          console.log(`🔧 发现工具调用 #${toolCallCount}:`);
          console.log(`   工具名称: ${content.name}`);
          console.log(`   文件路径: ${content.input.path || '未指定'}`);
        }
      }
    }

    console.log(`\n📊 总计工具调用次数: ${toolCallCount}`);
    
    // 检查是否有重复的文件写入操作
    const writeFileCalls = toolCalls.filter(call => call.name === 'write_file');
    const uniquePaths = new Set(writeFileCalls.map(call => call.input.path));
    
    if (writeFileCalls.length === 1) {
      console.log('✅ 测试通过：只有一次文件写入操作');
    } else if (writeFileCalls.length > 1 && uniquePaths.size === 1) {
      console.log('❌ 测试失败：检测到对同一文件的重复写入操作');
      console.log('重复的文件路径:', Array.from(uniquePaths));
    } else if (writeFileCalls.length > 1 && uniquePaths.size > 1) {
      console.log('⚠️  检测到多个文件写入操作，但路径不同（可能是正常的多文件操作）');
      console.log('文件路径:', Array.from(uniquePaths));
    }

  } catch (error) {
    console.error('❌ 复杂场景测试失败:', error.message);
  }
}

// 运行所有测试
async function runAllTests() {
  console.log('🚀 开始重复工具调用修复验证测试...\n');
  
  await testSimpleFileWrite();
  
  console.log('\n' + '='.repeat(60) + '\n');
  
  await testComplexScenario();
  
  console.log('\n🎉 所有测试完成！');
  console.log('\n💡 如果看到 "只有一次工具调用" 的消息，说明重复调用问题已修复。');
}

// 运行测试
runAllTests().catch(console.error);
