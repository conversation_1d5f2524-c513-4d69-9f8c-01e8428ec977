// 测试MCP工具集成逻辑
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试MCP工具集成
async function testMCPIntegration() {
  console.log('🧪 测试MCP工具集成逻辑\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '写3个笑话，写到joke.txt文件内'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送MCP工具集成请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 响应成功接收');
    
    // 分析响应结构
    console.log('\n📊 响应分析:');
    console.log('响应类型:', data.type || 'unknown');
    console.log('停止原因:', data.stop_reason || 'unknown');
    
    let textBlocks = 0;
    let toolUseBlocks = 0;
    let toolResultBlocks = 0;
    
    if (data.content && Array.isArray(data.content)) {
      console.log('内容块数量:', data.content.length);
      
      data.content.forEach((content, index) => {
        console.log(`\n内容块 ${index + 1}:`);
        console.log(`  类型: ${content.type}`);
        
        if (content.type === 'text') {
          textBlocks++;
          console.log(`  文本长度: ${content.text.length} 字符`);
          console.log(`  文本预览: ${content.text.substring(0, 100)}...`);
        } else if (content.type === 'tool_use') {
          toolUseBlocks++;
          console.log(`  工具名称: ${content.name}`);
          console.log(`  工具ID: ${content.id}`);
          console.log(`  工具参数:`, JSON.stringify(content.input, null, 2));
        } else if (content.type === 'tool_result') {
          toolResultBlocks++;
          console.log(`  工具结果ID: ${content.tool_use_id}`);
          console.log(`  是否错误: ${content.is_error || false}`);
          
          // 尝试解析工具结果
          try {
            const resultData = JSON.parse(content.content);
            console.log(`  结果类型: ${typeof resultData}`);
            if (resultData && resultData.content) {
              console.log(`  结果内容: ${JSON.stringify(resultData.content).substring(0, 100)}...`);
            }
          } catch (e) {
            console.log(`  结果内容: ${content.content.substring(0, 100)}...`);
          }
        }
      });
    }
    
    console.log(`\n📈 统计:`);
    console.log(`- 文本块: ${textBlocks}`);
    console.log(`- 工具使用块: ${toolUseBlocks}`);
    console.log(`- 工具结果块: ${toolResultBlocks}`);
    
    // 判断测试结果
    if (toolUseBlocks > 0 && toolResultBlocks > 0) {
      console.log('\n🎉 测试成功：MCP工具集成正常工作！');
      console.log('   ✅ 检测到工具调用');
      console.log('   ✅ 执行了MCP工具');
      console.log('   ✅ 返回了工具结果');
      
      if (toolResultBlocks === toolUseBlocks) {
        console.log('   ✅ 工具调用和结果数量匹配');
      } else {
        console.log('   ⚠️  工具调用和结果数量不匹配');
      }
    } else if (toolUseBlocks > 0 && toolResultBlocks === 0) {
      console.log('\n⚠️  部分成功：有工具调用但没有结果');
      console.log('   可能原因：MCP工具执行失败');
    } else if (toolUseBlocks === 0) {
      console.log('\n⚠️  没有工具调用：可能是纯文本响应');
    } else {
      console.log('\n❌ 测试失败：响应格式异常');
    }

    // 显示完整响应以供调试
    console.log('\n📄 完整响应:');
    console.log(JSON.stringify(data, null, 2));

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testMCPIntegration().catch(console.error);
