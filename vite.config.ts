import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [vue()],
  base: './', // 设置为相对路径，这样打包后的资源引用路径正确
  build: {
    outDir: 'dist', // 输出目录
    emptyOutDir: true, // 清空输出目录
  },
  server: {
    port: 5173, // 开发服务器端口
    strictPort: true, // 如果端口被占用，则会直接退出
    host: '127.0.0.1', // 使用IP地址而不是hostname
    proxy: {
      // 将所有/api前缀的请求转发到后端服务器
      '/api': {
        target: 'http://localhost:3001', // 后端服务器地址
        changeOrigin: true, // 改变请求来源
        secure: false, // 接受无效证书
        rewrite: (path) => path // 不重写路径
      }
    }
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src') // 路径别名
    }
  }
})
