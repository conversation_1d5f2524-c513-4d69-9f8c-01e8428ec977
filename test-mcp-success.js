// 测试MCP工具成功执行
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试MCP工具成功执行
async function testMCPSuccess() {
  console.log('🧪 测试MCP工具成功执行\n');

  const requestBody = {
    model: 'claude-3-haiku-20240307',
    max_tokens: 1000,
    messages: [
      {
        role: 'user',
        content: '写3个笑话，写到Desktop/joke.txt文件内'
      }
    ],
    tools: [
      {
        name: 'write_file',
        description: 'Write content to a file',
        input_schema: {
          type: 'object',
          properties: {
            path: {
              type: 'string',
              description: 'The file path to write to'
            },
            content: {
              type: 'string',
              description: 'The content to write to the file'
            }
          },
          required: ['path', 'content']
        }
      }
    ],
    temperature: 0.7
  };

  try {
    console.log('发送MCP工具成功执行请求...');
    
    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`请求失败: ${response.status}`, errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ 响应成功接收');
    
    // 分析响应结构
    console.log('\n📊 响应分析:');
    console.log('响应类型:', data.type || 'unknown');
    console.log('停止原因:', data.stop_reason || 'unknown');
    
    let toolUseBlock = null;
    let toolResultBlock = null;
    
    if (data.content && Array.isArray(data.content)) {
      console.log('内容块数量:', data.content.length);
      
      data.content.forEach((content, index) => {
        console.log(`\n内容块 ${index + 1}:`);
        console.log(`  类型: ${content.type}`);
        
        if (content.type === 'tool_use') {
          toolUseBlock = content;
          console.log(`  工具名称: ${content.name}`);
          console.log(`  工具ID: ${content.id}`);
          console.log(`  工具参数:`, JSON.stringify(content.input, null, 2));
        } else if (content.type === 'tool_result') {
          toolResultBlock = content;
          console.log(`  工具结果ID: ${content.tool_use_id}`);
          console.log(`  是否错误: ${content.is_error || false}`);
          
          // 解析工具结果
          try {
            const resultData = JSON.parse(content.content);
            console.log(`  结果解析成功:`);
            
            if (resultData.isError) {
              console.log(`    ❌ 执行失败: ${resultData.content?.[0]?.text || '未知错误'}`);
            } else {
              console.log(`    ✅ 执行成功: ${resultData.content?.[0]?.text || '操作完成'}`);
            }
          } catch (e) {
            console.log(`  结果内容: ${content.content}`);
          }
        }
      });
    }
    
    // 判断整体结果
    if (toolUseBlock && toolResultBlock) {
      console.log('\n🎯 工具调用分析:');
      console.log(`工具名称: ${toolUseBlock.name}`);
      console.log(`文件路径: ${toolUseBlock.input.path}`);
      console.log(`内容长度: ${toolUseBlock.input.content.length} 字符`);
      
      try {
        const resultData = JSON.parse(toolResultBlock.content);
        if (resultData.isError) {
          console.log('\n❌ 工具执行失败');
          console.log(`错误信息: ${resultData.content?.[0]?.text}`);
        } else {
          console.log('\n🎉 工具执行成功！');
          console.log(`成功信息: ${resultData.content?.[0]?.text}`);
          console.log('\n这就是图中显示的效果：');
          console.log('✅ 用户消息：写3个笑话，写到Desktop/joke.txt文件内');
          console.log('✅ 工具调用：write_file');
          console.log('✅ 调用成功：文件已创建');
          console.log('✅ 1个工具调用已完成');
        }
      } catch (e) {
        console.log('\n⚠️  工具结果解析失败');
      }
    }

  } catch (error) {
    console.error('❌ 测试失败:', error.message);
  }
}

// 运行测试
testMCPSuccess().catch(console.error);
