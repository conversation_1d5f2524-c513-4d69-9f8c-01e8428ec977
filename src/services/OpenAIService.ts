import OpenAI from 'openai';
import { getProviderById } from './ModelProviders';

interface LLMServiceOptions {
  apiKey?: string;
  baseUrl?: string;
  model?: string;
  providerId?: string;
}

export class LLMService {
  private client: OpenAI;
  private model: string;
  private baseURL: string;
  private providerId: string;
  private apiKey: string;
  private tools: Array<{name: string, description: string, inputSchema: any}> = [];

  constructor(options?: LLMServiceOptions) {
    // 获取API密钥，优先使用传入的选项
    this.apiKey = options?.apiKey || import.meta.env.VITE_API_KEY;

    if (!this.apiKey) {
      console.error('API密钥未设置');
      throw new Error('API密钥未设置，请在设置中配置API密钥');
    }

    // 确定提供商ID
    this.providerId = options?.providerId || localStorage.getItem('providerId') || import.meta.env.VITE_MODEL_PROVIDER || 'openai';

    // 获取提供商信息
    const provider = getProviderById(this.providerId);

    // 获取baseURL配置
    if (this.providerId === 'custom') {
      // 自定义提供商使用传入的baseUrl或localStorage中的customBaseUrl
      this.baseURL = options?.baseUrl || localStorage.getItem('customBaseUrl') || '';
    } else {
      // 其他提供商使用传入的baseUrl或提供商默认baseUrl
      this.baseURL = options?.baseUrl || (provider?.baseUrl || 'https://api.openai.com/v1');
    }

    console.log(`使用API基础URL: ${this.baseURL}`);

    // 初始化OpenAI实例
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      dangerouslyAllowBrowser: true // 在浏览器环境中使用
    });

    // 使用指定的模型或默认值
    if (this.providerId === 'custom') {
      this.model = options?.model || localStorage.getItem('customModelId') || 'gpt-3.5-turbo';
    } else {
      this.model = options?.model || localStorage.getItem('modelId') || import.meta.env.VITE_OPENAI_MODEL || 'gpt-3.5-turbo';
    }

    console.log(`使用模型: ${this.model}`);
  }

  /**
   * 获取当前使用的基础URL
   */
  getBaseURL(): string {
    return this.baseURL;
  }

  /**
   * 获取当前使用的模型
   */
  getModel(): string {
    return this.model;
  }

  /**
   * 获取当前使用的提供商ID
   */
  getProviderId(): string {
    return this.providerId;
  }

  /**
   * 设置新的API密钥
   * @param apiKey 新的API密钥
   */
  setApiKey(apiKey: string): void {
    if (!apiKey) {
      console.error('API密钥未设置');
      return;
    }

    // 更新实例的apiKey属性
    this.apiKey = apiKey;

    // 重新创建OpenAI实例，使用新的API密钥
    this.client = new OpenAI({
      apiKey: this.apiKey,
      baseURL: this.baseURL,
      dangerouslyAllowBrowser: true
    });

    console.log('API密钥已更新');
  }

  /**
   * 发送消息到LLM服务并获取回复
   * @param messages 消息历史
   * @param tools 可用工具列表
   */
  async sendMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    // 优先使用存储的工具列表，如果传入了tools参数则使用传入的
    const toolsToUse = tools || (this.tools.length > 0 ? this.tools : undefined);

    try {
      // 如果使用 Anthropic API
      if (this.providerId === 'anthropic') {
        return await this.sendAnthropicMessage(messages, toolsToUse);
      }

      // 其他提供商使用 OpenAI 格式
      // 准备工具定义
      const formattedTools = toolsToUse?.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema || {
            type: 'object',
            properties: {},
            required: []
          }
        }
      }));

      // 调用Chat Completions API
      const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 1000
      };

      // 只有当formattedTools有值且不为空数组时才添加tools参数
      // 特别是对DeepSeek API，空tools数组会导致400错误
      if (formattedTools && formattedTools.length > 0) {
        params.tools = formattedTools;
      }

      const response = await this.client.chat.completions.create(params);

      // 获取响应
      const responseMessage = response.choices[0].message;

      // 检查是否有工具调用
      if (responseMessage.tool_calls && responseMessage.tool_calls.length > 0) {
        return JSON.stringify({
          type: 'tool_calls',
          tool_calls: responseMessage.tool_calls.map(call => ({
            name: call.function.name,
            arguments: JSON.parse(call.function.arguments)
          }))
        });
      }

      return responseMessage.content || '无响应';
    } catch (error) {
      console.error('调用API出错:', error);
      throw new Error('调用API出错: ' + (error as Error).message);
    }
  }

  /**
   * 清理和验证 Anthropic 消息格式
   * @param messages 原始消息列表
   * @returns 清理后的消息列表
   */
  private cleanAnthropicMessages(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>
  ): Array<{role: 'user' | 'assistant', content: string}> {
    // 过滤和清理消息
    const cleanedMessages = messages
      .filter(msg => msg.role !== 'system') // 移除 system 消息
      .filter(msg => msg.content && typeof msg.content === 'string') // 确保有内容
      .map(msg => ({
        role: msg.role as 'user' | 'assistant',
        content: msg.content.trim()
      }))
      .filter(msg => msg.content.length > 0); // 移除空内容

    // 移除连续的相同角色消息（合并内容）
    const mergedMessages: Array<{role: 'user' | 'assistant', content: string}> = [];

    for (const msg of cleanedMessages) {
      if (mergedMessages.length === 0) {
        mergedMessages.push(msg);
      } else {
        const lastMsg = mergedMessages[mergedMessages.length - 1];
        if (lastMsg.role === msg.role) {
          // 合并相同角色的消息
          lastMsg.content += '\n' + msg.content;
        } else {
          mergedMessages.push(msg);
        }
      }
    }

    // 确保最后一条消息是用户消息
    while (mergedMessages.length > 0 &&
           mergedMessages[mergedMessages.length - 1].role === 'assistant') {
      mergedMessages.pop();
    }

    // 确保至少有一条消息
    if (mergedMessages.length === 0) {
      throw new Error('没有有效的消息内容，请检查消息历史');
    }

    // 确保第一条消息是用户消息
    if (mergedMessages[0].role !== 'user') {
      throw new Error('第一条消息必须是用户消息');
    }

    return mergedMessages;
  }

  /**
   * 发送消息到 Anthropic API
   * @param messages 消息历史
   * @param tools 可用工具列表
   */
  private async sendAnthropicMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    try {
      console.log('使用 Anthropic API 发送消息');
      console.log('原始消息数量:', messages.length);

      // 清理和验证消息格式
      const anthropicMessages = this.cleanAnthropicMessages(messages);

      // 提取 system 消息
      const systemMessage = messages.find(msg => msg.role === 'system')?.content;

      console.log('Anthropic 清理后消息数量:', anthropicMessages.length);
      console.log('Anthropic 消息列表:', anthropicMessages);

      // 转换工具格式
      const anthropicTools = tools?.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.inputSchema || {
          type: 'object',
          properties: {},
          required: []
        }
      }));

      // 构建请求体
      const requestBody: any = {
        model: this.model,
        max_tokens: 1000,
        messages: anthropicMessages,
        temperature: 0.7
      };

      // 添加 system prompt
      if (systemMessage) {
        requestBody.system = systemMessage;
      }

      // 添加工具
      if (anthropicTools && anthropicTools.length > 0) {
        requestBody.tools = anthropicTools;
      }

      console.log('Anthropic 请求体:', JSON.stringify(requestBody, null, 2));

      // 发送请求到代理路由
      const response = await fetch('/api/anthropic/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Anthropic API 错误: ${response.status}`, errorText);
        throw new Error(`Anthropic API 错误: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Anthropic API 响应:', data);

      // 解析响应
      if (data.content && data.content.length > 0) {
        const content = data.content[0];

        // 检查是否有工具调用
        if (content.type === 'tool_use') {
          return JSON.stringify({
            type: 'tool_calls',
            tool_calls: [{
              name: content.name,
              arguments: content.input
            }]
          });
        }

        // 返回文本内容
        if (content.type === 'text') {
          return content.text || '无响应';
        }
      }

      return '无响应';
    } catch (error) {
      console.error('Anthropic API 调用失败:', error);
      throw error;
    }
  }

  /**
   * 发送消息到LLM服务并获取流式回复
   * @param messages 消息历史
   * @param tools 可用工具列表
   * @param onChunk 接收数据块的回调函数
   */
  async sendStreamMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    onChunk: (chunk: string) => void,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    // 优先使用存储的工具列表，如果传入了tools参数则使用传入的
    const toolsToUse = tools || (this.tools.length > 0 ? this.tools : undefined);

    try {
      // 如果使用 Anthropic API
      if (this.providerId === 'anthropic') {
        return await this.sendAnthropicStreamMessage(messages, onChunk, toolsToUse);
      }

      // 其他提供商使用 OpenAI 格式
      // 准备工具定义
      const formattedTools = toolsToUse?.map(tool => ({
        type: 'function' as const,
        function: {
          name: tool.name,
          description: tool.description,
          parameters: tool.inputSchema || {
            type: 'object',
            properties: {},
            required: []
          }
        }
      }));

      // 调用Chat Completions API，启用流式响应
      const params: OpenAI.Chat.Completions.ChatCompletionCreateParams = {
        model: this.model,
        messages,
        temperature: 0.7,
        max_tokens: 1000,
        stream: true,
      };

      // 只有当formattedTools有值且不为空数组时才添加tools参数
      // 特别是对DeepSeek API，空tools数组会导致400错误
      if (formattedTools && formattedTools.length > 0) {
        params.tools = formattedTools;
      }

      const stream = await this.client.chat.completions.create(params);

      let fullResponse = '';
      let toolCallsData: any[] = [];
      let isToolCall = false;

      // 处理流式响应
      for await (const chunk of stream) {
        // 获取当前块的内容
        const content = chunk.choices[0]?.delta?.content || '';
        const toolCalls = chunk.choices[0]?.delta?.tool_calls || [];

        // 如果有工具调用
        if (toolCalls.length > 0) {
          isToolCall = true;

          // 将工具调用数据添加到集合中
          toolCalls.forEach(toolCall => {
            if (!toolCallsData.some(t => t.index === toolCall.index)) {
              toolCallsData.push({
                index: toolCall.index,
                id: toolCall.id,
                type: toolCall.type,
                function: {
                  name: toolCall.function?.name || '',
                  arguments: toolCall.function?.arguments || ''
                }
              });
            } else {
              // 更新现有工具调用的参数
              const existingTool = toolCallsData.find(t => t.index === toolCall.index);
              if (existingTool && toolCall.function?.arguments) {
                existingTool.function.arguments += toolCall.function.arguments;
              }
            }
          });
        }

        // 如果有文本内容，添加到完整响应
        if (content) {
          fullResponse += content;
          onChunk(content);
        }
      }

      // 如果是工具调用，返回工具调用数据的JSON
      if (isToolCall) {
        // 格式化工具调用数据
        const formattedToolCalls = toolCallsData.map(call => {
          try {
            // 尝试解析JSON参数
            return {
              name: call.function.name,
              arguments: JSON.parse(call.function.arguments)
            };
          } catch (e) {
            // 如果解析失败，返回原始字符串
            return {
              name: call.function.name,
              arguments: call.function.arguments
            };
          }
        });

        const toolCallResponse = JSON.stringify({
          type: 'tool_calls',
          tool_calls: formattedToolCalls
        });

        // 将工具调用响应传递给回调
        onChunk(toolCallResponse);
        return toolCallResponse;
      }

      return fullResponse;
    } catch (error) {
      console.error('调用流式API出错:', error);
      const errorMessage = '调用API出错: ' + (error as Error).message;
      onChunk(errorMessage);
      throw new Error(errorMessage);
    }
  }

  /**
   * 发送流式消息到 Anthropic API
   * @param messages 消息历史
   * @param onChunk 接收数据块的回调函数
   * @param tools 可用工具列表
   */
  private async sendAnthropicStreamMessage(
    messages: Array<{role: 'user' | 'assistant' | 'system', content: string}>,
    onChunk: (chunk: string) => void,
    tools?: Array<{name: string, description: string, inputSchema: any}>
  ): Promise<string> {
    try {
      console.log('使用 Anthropic API 发送流式消息');
      console.log('原始流式消息数量:', messages.length);

      // 清理和验证消息格式
      const anthropicMessages = this.cleanAnthropicMessages(messages);

      // 提取 system 消息
      const systemMessage = messages.find(msg => msg.role === 'system')?.content;

      console.log('Anthropic 清理后流式消息数量:', anthropicMessages.length);
      console.log('Anthropic 流式消息列表:', anthropicMessages);

      // 转换工具格式
      const anthropicTools = tools?.map(tool => ({
        name: tool.name,
        description: tool.description,
        input_schema: tool.inputSchema || {
          type: 'object',
          properties: {},
          required: []
        }
      }));

      // 构建请求体
      const requestBody: any = {
        model: this.model,
        max_tokens: 1000,
        messages: anthropicMessages,
        temperature: 0.7,
        stream: true
      };

      // 添加 system prompt
      if (systemMessage) {
        requestBody.system = systemMessage;
      }

      // 添加工具
      if (anthropicTools && anthropicTools.length > 0) {
        requestBody.tools = anthropicTools;
      }

      console.log('Anthropic 流式请求体:', JSON.stringify(requestBody, null, 2));

      // 发送请求到代理路由
      const response = await fetch('/api/anthropic/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': this.apiKey,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Anthropic API 错误: ${response.status}`, errorText);
        throw new Error(`Anthropic API 错误: ${response.status} - ${errorText}`);
      }

      // 处理流式响应
      const reader = response.body?.getReader();
      if (!reader) {
        throw new Error('无法获取响应流');
      }

      let fullResponse = '';
      const decoder = new TextDecoder();

      try {
        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value, { stream: true });
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              const data = line.slice(6);
              if (data === '[DONE]') {
                break;
              }

              try {
                const parsed = JSON.parse(data);

                // 处理文本内容
                if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                  const text = parsed.delta.text;
                  fullResponse += text;
                  onChunk(text);
                }

                // 处理工具调用
                if (parsed.type === 'content_block_start' && parsed.content_block?.type === 'tool_use') {
                  const toolCall = parsed.content_block;
                  const toolResponse = JSON.stringify({
                    type: 'tool_calls',
                    tool_calls: [{
                      name: toolCall.name,
                      arguments: toolCall.input
                    }]
                  });
                  fullResponse = toolResponse;
                  onChunk(toolResponse);
                  break;
                }
              } catch (parseError) {
                console.warn('解析 SSE 数据失败:', parseError);
              }
            }
          }
        }
      } finally {
        reader.releaseLock();
      }

      return fullResponse;
    } catch (error) {
      console.error('Anthropic 流式 API 调用失败:', error);
      const errorMessage = 'Anthropic API 错误: ' + (error as Error).message;
      onChunk(errorMessage);
      throw error;
    }
  }

  /**
   * 获取可用模型列表
   * @param provider 可选的提供商参数，用于覆盖实例的providerId
   * @returns 模型列表
   */
  async getModels(provider?: string): Promise<Array<{id: string, name: string, display_name?: string, created_at?: string}>> {
    try {
      const targetProvider = provider || this.providerId;

      // 如果使用Anthropic API
      if (targetProvider === 'anthropic') {
        console.log('获取Anthropic模型列表');

        // 使用代理路由获取Anthropic模型列表
        const response = await fetch('/api/anthropic/models', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'x-api-key': this.apiKey,
            'anthropic-version': '2023-06-01'
          }
        });

        if (!response.ok) {
          const errorText = await response.text();
          console.error(`获取Anthropic模型列表失败: ${response.status}`, errorText);
          throw new Error(`获取Anthropic模型列表失败: ${response.status} - ${errorText}`);
        }

        const data = await response.json();
        console.log('获取到Anthropic模型列表:', data);

        // 检查响应格式，Anthropic API返回的是 data.data 数组
        const models = data.data || data.models || [];

        if (!Array.isArray(models)) {
          console.error('Anthropic API返回的模型数据格式不正确:', data);
          throw new Error('模型数据格式不正确');
        }

        // 将Anthropic模型转换为标准格式
        return models.map((model: any) => ({
          id: model.id,
          name: model.display_name || model.id,
          display_name: model.display_name,
          created_at: model.created_at
        }));
      }
      // 如果使用OpenAI API
      else if (targetProvider !== 'custom' && this.client) {
        const response = await this.client.models.list();
        return response.data.map(model => ({
          id: model.id,
          name: model.id
        }));
      } else {
        // 如果是自定义提供商，返回空列表
        return [];
      }
    } catch (error) {
      console.error('获取模型列表失败:', error);
      throw error; // 重新抛出错误，让调用者处理
    }
  }

  /**
   * 更新工具列表
   * @param tools 新的工具列表
   */
  updateTools(tools: Array<{name: string, description: string, inputSchema: any}>): void {
    console.log(`LLMService: 更新工具列表，数量 ${tools.length}`);
    // 保存工具列表到实例变量中，以便在后续API调用中使用
    this.tools = [...tools];
  }
}