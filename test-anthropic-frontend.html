<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anthropic API 前端测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #1677ff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        button:hover {
            background-color: #0958d9;
        }
        button:disabled {
            background-color: #d9d9d9;
            cursor: not-allowed;
        }
        .response {
            background-color: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .loading {
            color: #666;
            font-style: italic;
        }
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        .success {
            color: #155724;
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin-bottom: 10px;
            box-sizing: border-box;
        }
        textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🤖 Anthropic API 前端测试</h1>
        
        <div class="test-section">
            <h3>📝 自定义消息测试</h3>
            <div>
                <label>消息内容:</label>
                <textarea id="customMessage" placeholder="输入您想发送给 Claude 的消息...">Hello Claude! Please introduce yourself and tell me what you can do.</textarea>
            </div>
            <div>
                <label>System Prompt (可选):</label>
                <input type="text" id="systemPrompt" placeholder="例如: You are a helpful assistant...">
            </div>
            <button onclick="testCustomMessage()">发送消息</button>
            <button onclick="testCustomStreamMessage()">发送流式消息</button>
            <div id="customResponse" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🎯 快速测试</h3>
            <button onclick="testSimpleMessage()">简单问候</button>
            <button onclick="testJoke()">讲个笑话</button>
            <button onclick="testStory()">写个故事</button>
            <button onclick="testCode()">写代码</button>
            <div id="quickResponse" class="response" style="display: none;"></div>
        </div>

        <div class="test-section">
            <h3>🔧 LLMService 测试</h3>
            <p>测试前端 LLMService 类的 Anthropic 集成</p>
            <button onclick="testLLMService()">测试 LLMService</button>
            <button onclick="testLLMServiceStream()">测试流式 LLMService</button>
            <div id="llmResponse" class="response" style="display: none;"></div>
        </div>
    </div>

    <script type="module">
        const API_KEY = '************************************************************************************************************';

        // 显示响应
        function showResponse(elementId, content, isError = false, isSuccess = false) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response';
            if (isError) element.className += ' error';
            if (isSuccess) element.className += ' success';
            element.textContent = content;
        }

        // 追加响应内容（用于流式）
        function appendResponse(elementId, content) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = 'response success';
            element.textContent += content;
        }

        // 发送消息到 Anthropic API
        async function sendMessage(messages, system = null, stream = false, responseElementId) {
            try {
                showResponse(responseElementId, '正在发送请求...', false, false);

                const requestBody = {
                    model: 'claude-3-haiku-20240307',
                    max_tokens: 1000,
                    messages: messages,
                    temperature: 0.7
                };

                if (system) {
                    requestBody.system = system;
                }

                if (stream) {
                    requestBody.stream = true;
                }

                const response = await fetch('/api/anthropic/messages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'x-api-key': API_KEY,
                        'anthropic-version': '2023-06-01'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`API 错误: ${response.status} - ${errorText}`);
                }

                if (stream) {
                    showResponse(responseElementId, '', false, true);
                    const reader = response.body.getReader();
                    const decoder = new TextDecoder();

                    while (true) {
                        const { done, value } = await reader.read();
                        if (done) break;

                        const chunk = decoder.decode(value, { stream: true });
                        const lines = chunk.split('\\n');

                        for (const line of lines) {
                            if (line.startsWith('data: ')) {
                                const data = line.slice(6);
                                if (data === '[DONE]') break;

                                try {
                                    const parsed = JSON.parse(data);
                                    if (parsed.type === 'content_block_delta' && parsed.delta?.text) {
                                        appendResponse(responseElementId, parsed.delta.text);
                                    }
                                } catch (e) {
                                    // 忽略解析错误
                                }
                            }
                        }
                    }
                } else {
                    const data = await response.json();
                    if (data.content && data.content.length > 0) {
                        const content = data.content[0];
                        if (content.type === 'text') {
                            showResponse(responseElementId, content.text, false, true);
                        }
                    } else {
                        showResponse(responseElementId, '收到空响应', true);
                    }
                }

            } catch (error) {
                showResponse(responseElementId, `错误: ${error.message}`, true);
            }
        }

        // 自定义消息测试
        window.testCustomMessage = async function() {
            const message = document.getElementById('customMessage').value;
            const system = document.getElementById('systemPrompt').value;
            
            if (!message.trim()) {
                showResponse('customResponse', '请输入消息内容', true);
                return;
            }

            const messages = [{ role: 'user', content: message }];
            await sendMessage(messages, system || null, false, 'customResponse');
        };

        // 自定义流式消息测试
        window.testCustomStreamMessage = async function() {
            const message = document.getElementById('customMessage').value;
            const system = document.getElementById('systemPrompt').value;
            
            if (!message.trim()) {
                showResponse('customResponse', '请输入消息内容', true);
                return;
            }

            const messages = [{ role: 'user', content: message }];
            await sendMessage(messages, system || null, true, 'customResponse');
        };

        // 快速测试函数
        window.testSimpleMessage = () => {
            const messages = [{ role: 'user', content: 'Hello! How are you today?' }];
            sendMessage(messages, null, false, 'quickResponse');
        };

        window.testJoke = () => {
            const messages = [{ role: 'user', content: 'Tell me a funny joke about artificial intelligence.' }];
            sendMessage(messages, null, false, 'quickResponse');
        };

        window.testStory = () => {
            const messages = [{ role: 'user', content: 'Write a short story about a time-traveling scientist.' }];
            sendMessage(messages, null, true, 'quickResponse');
        };

        window.testCode = () => {
            const messages = [{ role: 'user', content: 'Write a Python function to calculate the factorial of a number.' }];
            sendMessage(messages, 'You are a helpful programming assistant.', false, 'quickResponse');
        };

        // LLMService 测试
        window.testLLMService = async function() {
            try {
                showResponse('llmResponse', '正在测试 LLMService...', false, false);

                // 动态导入 LLMService（这在实际应用中可能需要不同的方式）
                const { LLMService } = await import('/src/services/OpenAIService.ts');
                
                const llmService = new LLMService({
                    apiKey: API_KEY,
                    providerId: 'anthropic',
                    model: 'claude-3-haiku-20240307'
                });

                const messages = [
                    { role: 'user', content: 'Hello! Please explain what you are in one sentence.' }
                ];

                const response = await llmService.sendMessage(messages);
                showResponse('llmResponse', `LLMService 响应:\\n${response}`, false, true);

            } catch (error) {
                showResponse('llmResponse', `LLMService 错误: ${error.message}`, true);
            }
        };

        window.testLLMServiceStream = async function() {
            try {
                showResponse('llmResponse', '正在测试流式 LLMService...', false, true);

                const { LLMService } = await import('/src/services/OpenAIService.ts');
                
                const llmService = new LLMService({
                    apiKey: API_KEY,
                    providerId: 'anthropic',
                    model: 'claude-3-haiku-20240307'
                });

                const messages = [
                    { role: 'user', content: 'Tell me about the benefits of renewable energy in 3 paragraphs.' }
                ];

                await llmService.sendStreamMessage(messages, (chunk) => {
                    appendResponse('llmResponse', chunk);
                });

            } catch (error) {
                showResponse('llmResponse', `流式 LLMService 错误: ${error.message}`, true);
            }
        };

        console.log('🚀 Anthropic API 测试页面已加载');
    </script>
</body>
</html>
