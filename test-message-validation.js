// 测试消息验证和清理功能
import fetch from 'node-fetch';

const API_KEY = '************************************************************************************************************';

// 测试各种问题消息格式
async function testProblematicMessages() {
  console.log('🧪 测试问题消息格式的处理...\n');

  const testCases = [
    {
      name: '包含空消息的对话',
      messages: [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
        { role: 'user', content: '' }, // 空消息
        { role: 'assistant', content: 'How can I help?' },
        { role: 'user', content: 'Tell me a joke' }
      ]
    },
    {
      name: '以助手消息结尾的对话',
      messages: [
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi there!' },
        { role: 'user', content: 'How are you?' },
        { role: 'assistant', content: '' } // 空的助手消息结尾
      ]
    },
    {
      name: '连续的用户消息',
      messages: [
        { role: 'user', content: 'First message' },
        { role: 'user', content: 'Second message' },
        { role: 'user', content: 'Third message' }
      ]
    },
    {
      name: '包含 system 消息的对话',
      messages: [
        { role: 'system', content: 'You are a helpful assistant' },
        { role: 'user', content: 'Hello' },
        { role: 'assistant', content: 'Hi!' },
        { role: 'user', content: 'Tell me about yourself' }
      ]
    },
    {
      name: '只有空白字符的消息',
      messages: [
        { role: 'user', content: '   ' }, // 只有空格
        { role: 'assistant', content: '\n\t' }, // 只有换行和制表符
        { role: 'user', content: 'Real message here' }
      ]
    }
  ];

  for (const testCase of testCases) {
    console.log(`📝 测试: ${testCase.name}`);
    console.log('原始消息:', JSON.stringify(testCase.messages, null, 2));

    try {
      const requestBody = {
        model: 'claude-3-haiku-20240307',
        max_tokens: 500,
        messages: testCase.messages,
        temperature: 0.7
      };

      const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'x-api-key': API_KEY,
          'anthropic-version': '2023-06-01'
        },
        body: JSON.stringify(requestBody)
      });

      if (response.ok) {
        const data = await response.json();
        console.log('✅ 成功处理');
        if (data.content && data.content[0] && data.content[0].text) {
          console.log('响应:', data.content[0].text.substring(0, 100) + '...');
        }
      } else {
        const errorText = await response.text();
        console.log('❌ 失败:', errorText);
      }

    } catch (error) {
      console.log('❌ 错误:', error.message);
    }

    console.log('---\n');
    
    // 等待一下避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// 测试长对话历史
async function testLongConversation() {
  console.log('📚 测试长对话历史处理...\n');

  // 创建一个包含很多消息的对话
  const messages = [
    { role: 'system', content: 'You are a helpful assistant.' }
  ];

  // 添加多轮对话
  for (let i = 1; i <= 20; i++) {
    messages.push({ role: 'user', content: `User message ${i}` });
    messages.push({ role: 'assistant', content: `Assistant response ${i}` });
    
    // 随机添加一些空消息
    if (i % 5 === 0) {
      messages.push({ role: 'user', content: '' });
      messages.push({ role: 'assistant', content: '   ' });
    }
  }

  // 最后添加一个真实的用户问题
  messages.push({ role: 'user', content: 'Can you summarize our conversation?' });

  console.log(`原始消息数量: ${messages.length}`);

  try {
    const requestBody = {
      model: 'claude-3-haiku-20240307',
      max_tokens: 1000,
      messages: messages,
      temperature: 0.7
    };

    const response = await fetch('http://127.0.0.1:3001/api/anthropic/messages', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'x-api-key': API_KEY,
        'anthropic-version': '2023-06-01'
      },
      body: JSON.stringify(requestBody)
    });

    if (response.ok) {
      const data = await response.json();
      console.log('✅ 长对话处理成功');
      if (data.content && data.content[0] && data.content[0].text) {
        console.log('响应:', data.content[0].text.substring(0, 200) + '...');
      }
    } else {
      const errorText = await response.text();
      console.log('❌ 长对话处理失败:', errorText);
    }

  } catch (error) {
    console.log('❌ 长对话处理错误:', error.message);
  }
}

// 运行所有测试
async function runAllValidationTests() {
  console.log('🚀 开始消息验证测试...\n');
  
  await testProblematicMessages();
  
  console.log('\n' + '='.repeat(50) + '\n');
  
  await testLongConversation();
  
  console.log('\n🎉 消息验证测试完成！');
}

// 运行测试
runAllValidationTests().catch(console.error);
